#!/usr/bin/env python3
"""
Very small Pac-Man clone in pure Python 3 + tkinter.
Author: You
"""

import tkinter as tk
import random
import enum
import math

# ------------------------ Constants ------------------------

TILE = 24                 # pixels per maze cell
COLS, ROWS = 19, 21       # logical grid size
WIDTH  = COLS * TILE
HEIGHT = ROWS * TILE
FPS    = 60
SPEED  = 2                # pixels per frame for Pac-Man
GHOST_SPEED = 1.7

# Maze layout: 1 = wall, 0 = empty, 2 = dot
MAZE = [
    "1111111111111111111",
    "1000020000200000201",
    "1021112111211121101",
    "1000000000000000001",
    "1211121110111211121",
    "1000000101000000001",
    "1011110101011111101",
    "1010000000000000101",
    "1010111111111110101",
    "1010100001000010101",
    "1010111111111110101",
    "1010000000000000101",
    "1011111101011111101",
    "1000000001000000001",
    "1211121111211121121",
    "1000000000000000001",
    "1112111211121112111",
    "1000000000000000001",
    "1111111111111111111"
]

# ------------------------ Geometry helpers ------------------------

def grid_to_pixel(r, c):
    return c * TILE + TILE//2, r * TILE + TILE//2

def pixel_to_grid(x, y):
    return int(y // TILE), int(x // TILE)

def is_wall(r, c):
    return 0 <= r < len(MAZE) and 0 <= c < len(MAZE[0]) and MAZE[r][c] == "1"

# ------------------------ Classes ------------------------

class Direction(enum.Enum):
    STOP = 0
    UP   = 1
    DOWN = 2
    LEFT = 3
    RIGHT= 4

    def delta(self):
        return {
            Direction.STOP: (0, 0),
            Direction.UP:   (0, -1),
            Direction.DOWN: (0,  1),
            Direction.LEFT: (-1, 0),
            Direction.RIGHT:( 1, 0),
        }[self]

class Movable:
    def __init__(self, r, c, color):
        self.r, self.c = r, c
        self.x, self.y = grid_to_pixel(r, c)
        self.color = color
        self.dir = Direction.STOP
        self.next_dir = Direction.STOP

    def center_in_tile(self):
        return abs(self.x - (self.c*TILE + TILE//2)) < SPEED and \
               abs(self.y - (self.r*TILE + TILE//2)) < SPEED

    def snap(self):
        self.x, self.y = grid_to_pixel(self.r, self.c)

    def update(self):
        if self.center_in_tile():
            # commit queued direction if possible
            dr, dc = self.next_dir.delta()
            nr, nc = self.r + dr, self.c + dc
            if not is_wall(nr, nc):
                self.dir = self.next_dir
            else:
                # try current direction again
                dr, dc = self.dir.delta()
                nr, nc = self.r + dr, self.c + dc
                if is_wall(nr, nc):
                    self.dir = Direction.STOP
            self.r, self.c = pixel_to_grid(self.x, self.y)
            self.snap()

        # move
        dx, dy = self.dir.delta()
        self.x += dx * self.speed
        self.y += dy * self.speed

class PacMan(Movable):
    speed = SPEED
    def __init__(self, r, c):
        super().__init__(r, c, "yellow")

class Ghost(Movable):
    speed = GHOST_SPEED
    def __init__(self, r, c):
        super().__init__(r, c, "red")
        self.dir = Direction.LEFT

    def update(self):
        super().update()
        # simple AI: random direction at each intersection
        if self.center_in_tile():
            choices = [d for d in Direction if d != Direction.STOP]
            random.shuffle(choices)
            for d in choices:
                dr, dc = d.delta()
                nr, nc = self.r + dr, self.c + dc
                if not is_wall(nr, nc):
                    self.next_dir = d
                    break

# ------------------------ Game ------------------------

class Game:
    def __init__(self, root):
        self.root = root
        self.root.title("Tiny Pac-Man")
        self.canvas = tk.Canvas(root, width=WIDTH, height=HEIGHT, bg="black",
                                highlightthickness=0)
        self.canvas.pack()
        self.canvas.focus_set()
        self.root.bind("<KeyPress>", self.key)
        self.reset()
        self.after = None
        self.running = True
        self.started = False
        self.loop()

    def reset(self):
        self.pacman = PacMan(len(MAZE)//2, 9)
        self.ghost  = Ghost(len(MAZE)//2, 9)
        self.dots = {(r, c) for r in range(len(MAZE)) for c in range(len(MAZE[0]))
                     if MAZE[r][c] in ("0", "2")}
        self.game_over = False
        self.won = False
        self.started = False

    def key(self, ev):
        k = ev.keysym
        if k in ("q", "Escape"):
            self.quit()
            return
        if k == "space":
            if not self.started:
                self.started = True
            elif self.game_over or self.won:
                self.reset()
            return
        if self.started:
            mapping = {"Up": Direction.UP, "Down": Direction.DOWN,
                       "Left": Direction.LEFT, "Right": Direction.RIGHT}
            if k in mapping:
                self.pacman.next_dir = mapping[k]

    def quit(self):
        self.running = False
        if self.after:
            self.root.after_cancel(self.after)
        self.root.destroy()

    def loop(self):
        if not self.running:
            return
        self.update()
        self.draw()
        self.after = self.root.after(1000 // FPS, self.loop)

    def update(self):
        if not self.started or self.game_over or self.won:
            return
        self.pacman.update()
        self.ghost.update()

        # eat dot
        pos = (self.pacman.r, self.pacman.c)
        if pos in self.dots:
            self.dots.remove(pos)

        # win?
        if not self.dots:
            self.won = True

        # collision?
        dist = math.hypot(self.pacman.x - self.ghost.x,
                          self.pacman.y - self.ghost.y)
        if dist < TILE * 0.8:
            self.game_over = True

    def draw(self):
        self.canvas.delete("all")
        # maze walls
        for r in range(len(MAZE)):
            for c in range(len(MAZE[0])):
                if MAZE[r][c] == "1":
                    x, y = c*TILE, r*TILE
                    self.canvas.create_rectangle(x, y, x+TILE, y+TILE,
                                                 fill="blue", outline="")
        # dots
        for (r, c) in self.dots:
            x, y = grid_to_pixel(r, c)
            size = 5 if MAZE[r][c] == "2" else 3
            self.canvas.create_oval(x-size, y-size, x+size, y+size,
                                    fill="white", outline="")
        # actors
        for obj in (self.pacman, self.ghost):
            x, y = obj.x, obj.y
            self.canvas.create_oval(x-10, y-10, x+10, y+10,
                                    fill=obj.color, outline="")

        # messages
        if not self.started:
            self.canvas.create_text(WIDTH//2, HEIGHT//2-20,
                                    text="PRESS SPACE TO START", fill="white",
                                    font=("Arial", 20))
            self.canvas.create_text(WIDTH//2, HEIGHT//2+20,
                                    text="Use Arrow Keys to Move", fill="gray",
                                    font=("Arial", 14))
        elif self.game_over:
            self.canvas.create_text(WIDTH//2, HEIGHT//2-20,
                                    text="GAME OVER", fill="red",
                                    font=("Arial", 32))
            self.canvas.create_text(WIDTH//2, HEIGHT//2+20,
                                    text="Press SPACE to restart", fill="white",
                                    font=("Arial", 16))
        elif self.won:
            self.canvas.create_text(WIDTH//2, HEIGHT//2-20,
                                    text="YOU WIN!", fill="green",
                                    font=("Arial", 32))
            self.canvas.create_text(WIDTH//2, HEIGHT//2+20,
                                    text="Press SPACE to restart", fill="white",
                                    font=("Arial", 16))

# ------------------------ Main ------------------------

if __name__ == "__main__":
    root = tk.Tk()
    game = Game(root)
    root.mainloop()