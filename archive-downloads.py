import os
import shutil
import time
from datetime import datetime, timedelta

# Source and destination directories
SOURCE_DIR = "/Users/<USER>/Library/CloudStorage/OneDrive-Personal/Documents/Downloads"
DEST_DIR = "/Users/<USER>/Library/CloudStorage/Dropbox/Shared-mfmfazrin"

# Days threshold
DAYS_OLD = 60

# Calculate the cutoff timestamp
now = datetime.now()
cutoff = now - timedelta(days=DAYS_OLD)
cutoff_timestamp = cutoff.timestamp()

def move_old_files(source_dir, dest_dir, cutoff_ts):
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
    
    for filename in os.listdir(source_dir):
        file_path = os.path.join(source_dir, filename)
        if os.path.isfile(file_path):
            last_modified = os.path.getmtime(file_path)
            if last_modified < cutoff_ts:
                dest_path = os.path.join(dest_dir, filename)
                print(f"Moving: {file_path} -> {dest_path}")
                shutil.move(file_path, dest_path)

if __name__ == "__main__":
    move_old_files(SOURCE_DIR, DEST_DIR, cutoff_timestamp)
